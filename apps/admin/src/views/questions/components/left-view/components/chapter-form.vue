<script setup lang="ts">
import { useNaiveForm } from '@sa/hooks'

defineOptions({
  name: 'ChapterForm',
})

const modelInfo = defineModel('modelInfo', {
  type: Object as PropType<{
    ChapterName: string
    AdditionalRequirements: string
  }>,
  default: () => ({
    ChapterName: '',
    AdditionalRequirements: '',
  }),
})

// 表单引用和校验
const { formRef, validate } = useNaiveForm()

// 表单校验规则
const rules = {
  ChapterName: [
    {
      required: true,
      message: '请输入章节名称',
      trigger: ['blur', 'change'],
      validator: (_rule: any, value: string) => {
        if (!value || value.trim() === '') {
          return new Error('请输入章节名称')
        }
        if (value.trim().length < 2) {
          return new Error('章节名称至少需要2个字符')
        }
        return true
      },
    },
  ],
}

// 暴露校验方法给父组件
defineExpose({
  validate,
})

function selectKnowledgePoint() {
}
</script>

<template>
  <div class="p-12px">
    <NForm ref="formRef" :model="modelInfo" :rules="rules">
      <!-- 出题范围标题 -->
      <div class="mb-16px flex items-center">
        <SvgIcon icon="mdi:file-document-outline" class="mr-8px text-20px text-blue-500" />
        <span class="text-14px text-[#333] font-500">出题范围</span>
      </div>

      <!-- 章节名称输入 -->
      <NFormItem path="ChapterName">
        <template #label>
          <span class="text-left text-14px text-[#464646] font-500">章节名称</span>
        </template>
        <NInput
          v-model:value="modelInfo.ChapterName"
          placeholder="请输入章节名称"
          clearable
          maxlength="100"
          show-count
        />
      </NFormItem>

      <!-- 知识点选择区域 -->
      <div
        class="mb-24px min-h-200px flex flex-col cursor-pointer items-center justify-center border-2 border-gray-300 rounded-8px border-dashed bg-gray-50 p-24px transition-all duration-200 hover:border-blue-400 hover:bg-blue-50"
        @click="selectKnowledgePoint"
      >
        <div class="h-full flex flex-col items-center justify-center">
          <!-- 加号图标 -->
          <div class="mb-12px h-48px w-48px flex items-center justify-center rounded-full bg-blue-500 text-white">
            <SvgIcon icon="mdi:plus" class="text-24px" />
          </div>
          <!-- 提示文字 -->
          <span class="text-14px text-gray-600 font-500">选择本课知识点</span>
        </div>
      </div>

      <!-- 补充内容区域 -->
      <div class="border border-gray-200 rounded-8px bg-white">
        <!-- 标题栏 -->
        <div
          class="flex items-center justify-between p-16px"
        >
          <span class="text-14px text-[#464646] font-500">补充内容（选填）</span>
        </div>

        <!-- 可折叠内容 -->
        <div
          class="overflow-hidden transition-all duration-300"
        >
          <div class="border-t border-gray-200 p-16px">
            <NInput
              v-model:value="modelInfo.AdditionalRequirements"
              type="textarea"
              placeholder="您可以补充特殊的出题要求。&#10;示例如下：&#10;题目中需要包含&quot;折射&quot;&quot;反射&quot;这两个词汇"
              :rows="4"
              clearable
              maxlength="500"
              show-count
              class="w-full"
            />
          </div>
        </div>
      </div>
    </NForm>
  </div>
</template>
